import { Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { RegionDict } from '../entity/region-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { User } from '../entity/user.entity';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class InitService {
  /**
   * 初始化数据库数据
   */
  async initializeData() {
    console.log('📋 开始检查并初始化数据库数据...');

    // 分别检查并初始化各个核心数据表
    await this.checkAndInitRegionDict();
    await this.checkAndInitTypeDict();
    await this.checkAndInitRelationshipDict();
    await this.checkAndInitDefaultUser();

    console.log('📋 数据库数据检查和初始化完成');
  }

  /**
   * 检查并初始化区域字典
   */
  private async checkAndInitRegionDict() {
    const regionCount = await RegionDict.count();
    if (regionCount === 0) {
      console.log('📋 检测到区域字典数据缺失，开始初始化...');
      await this.initRegionDict();
    } else {
      console.log('📋 区域字典数据正常');
    }
  }

  /**
   * 检查并初始化类型字典
   */
  private async checkAndInitTypeDict() {
    const typeCount = await TypeDict.count();
    if (typeCount === 0) {
      console.log('📋 检测到类型字典数据缺失，开始初始化...');
      await this.initTypeDict();
    } else {
      console.log('📋 类型字典数据正常');
    }
  }

  /**
   * 检查并初始化关系字典
   */
  private async checkAndInitRelationshipDict() {
    const relationCount = await RelationshipDict.count();
    if (relationCount === 0) {
      console.log('📋 检测到关系字典数据缺失，开始初始化...');
      await this.initRelationshipDict();
    } else {
      console.log('📋 关系字典数据正常');
    }
  }

  /**
   * 检查并初始化默认用户
   */
  private async checkAndInitDefaultUser() {
    // 检查是否存在管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' } as any,
    });

    if (!adminUser) {
      console.log('📋 检测到管理员用户缺失，开始创建...');
      await this.initDefaultUser();
    } else {
      console.log('📋 管理员用户存在');
      // 检查管理员用户的角色是否正确
      if (adminUser.role !== 'admin') {
        console.log('📋 检测到管理员用户角色异常，正在修复...');
        await adminUser.update({ role: 'admin' });
        console.log('  ✓ 管理员用户角色已修复');
      }
      // 检查管理员用户是否被禁用
      if (!adminUser.isActive) {
        console.log('📋 检测到管理员用户被禁用，正在启用...');
        await adminUser.update({ isActive: true });
        console.log('  ✓ 管理员用户已启用');
      }
    }
  }

  /**
   * 验证核心数据完整性
   */
  async validateCoreData(): Promise<{
    isValid: boolean;
    issues: string[];
    fixedIssues: string[];
  }> {
    const issues: string[] = [];
    const fixedIssues: string[] = [];

    console.log('📋 开始验证核心数据完整性...');

    // 检查管理员用户
    const adminUser = await User.findOne({
      where: { username: 'admin' } as any,
    });

    if (!adminUser) {
      issues.push('管理员用户不存在');
      try {
        await this.initDefaultUser();
        fixedIssues.push('已创建管理员用户');
      } catch (error) {
        issues.push(`创建管理员用户失败: ${error.message}`);
      }
    } else {
      if (adminUser.role !== 'admin') {
        issues.push('管理员用户角色异常');
        try {
          await adminUser.update({ role: 'admin' });
          fixedIssues.push('已修复管理员用户角色');
        } catch (error) {
          issues.push(`修复管理员用户角色失败: ${error.message}`);
        }
      }
      if (!adminUser.isActive) {
        issues.push('管理员用户被禁用');
        try {
          await adminUser.update({ isActive: true });
          fixedIssues.push('已启用管理员用户');
        } catch (error) {
          issues.push(`启用管理员用户失败: ${error.message}`);
        }
      }
    }

    // 检查基础字典数据
    const regionCount = await RegionDict.count();
    if (regionCount === 0) {
      issues.push('区域字典数据缺失');
      try {
        await this.initRegionDict();
        fixedIssues.push('已初始化区域字典数据');
      } catch (error) {
        issues.push(`初始化区域字典失败: ${error.message}`);
      }
    }

    const typeCount = await TypeDict.count();
    if (typeCount === 0) {
      issues.push('类型字典数据缺失');
      try {
        await this.initTypeDict();
        fixedIssues.push('已初始化类型字典数据');
      } catch (error) {
        issues.push(`初始化类型字典失败: ${error.message}`);
      }
    }

    const relationCount = await RelationshipDict.count();
    if (relationCount === 0) {
      issues.push('关系字典数据缺失');
      try {
        await this.initRelationshipDict();
        fixedIssues.push('已初始化关系字典数据');
      } catch (error) {
        issues.push(`初始化关系字典失败: ${error.message}`);
      }
    }

    const isValid = issues.length === 0;
    console.log(
      `📋 核心数据完整性验证完成，发现 ${issues.length} 个问题，修复 ${fixedIssues.length} 个问题`
    );

    return {
      isValid,
      issues,
      fixedIssues,
    };
  }

  /**
   * 初始化区域字典
   */
  private async initRegionDict() {
    const regionData = [
      {
        regionCode: 'REGION_XIAN',
        regionName: '西安',
        parentId: 1,
        status: 1,
        sort: 1,
        regionDesc: '西安市及其周边区域',
      },
    ];

    await RegionDict.bulkCreate(regionData);
    console.log('  ✓ 区域字典初始化完成');
  }

  /**
   * 初始化类型字典
   */
  private async initTypeDict() {
    const typeData = [
      {
        typeCode: 'TYPE_TEMPLE',
        typeName: '寺庙',
        parentId: null,
        status: 1,
        sort: 1,
        typeDesc: '佛教、道教等宗教建筑',
      },
      {
        typeCode: 'TYPE_PALACE',
        typeName: '宫殿',
        parentId: null,
        status: 1,
        sort: 2,
        typeDesc: '皇家宫殿建筑群',
      },
      {
        typeCode: 'TYPE_TOMB',
        typeName: '陵墓',
        parentId: null,
        status: 1,
        sort: 3,
        typeDesc: '帝王陵墓、贵族墓葬',
      },
      {
        typeCode: 'TYPE_TOWER',
        typeName: '塔楼',
        parentId: null,
        status: 1,
        sort: 4,
        typeDesc: '佛塔、古塔等高层建筑',
      },
      {
        typeCode: 'TYPE_CITY_WALL',
        typeName: '城墙',
        parentId: null,
        status: 1,
        sort: 5,
        typeDesc: '古代城市防御工事',
      },
      {
        typeCode: 'TYPE_BRIDGE',
        typeName: '古桥',
        parentId: null,
        status: 1,
        sort: 6,
        typeDesc: '历史悠久的桥梁建筑',
      },
    ];

    await TypeDict.bulkCreate(typeData);
    console.log('  ✓ 类型字典初始化完成');
  }

  /**
   * 初始化关系字典
   */
  private async initRelationshipDict() {
    const relationData = [
      {
        relationCode: 'RELATION_LOCATION',
        relationName: '选址关联',
        parentId: null,
        status: 1,
        sort: 1,
        relationDesc: '山塬与城镇、建筑的选址依赖关系',
      },
      {
        relationCode: 'RELATION_VISUAL',
        relationName: '视线关联',
        parentId: null,
        status: 1,
        sort: 2,
        relationDesc: '不同要素间的视觉联系关系',
      },
      {
        relationCode: 'RELATION_WATER',
        relationName: '水系滋养',
        parentId: null,
        status: 1,
        sort: 3,
        relationDesc: '水系对周边地理要素的滋养关系',
      },
      {
        relationCode: 'RELATION_HISTORY',
        relationName: '历史沿革',
        parentId: null,
        status: 1,
        sort: 4,
        relationDesc: '历史要素间的时间演变关系',
      },
    ];

    await RelationshipDict.bulkCreate(relationData);
    console.log('  ✓ 关系字典初始化完成');
  }

  /**
   * 初始化默认用户
   */
  private async initDefaultUser() {
    // 创建默认管理员用户，密码会在实体setter中自动加密
    await User.create({
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      nickname: '系统管理员',
      isActive: true,
    } as any);

    console.log('  ✓ 默认管理员用户创建完成 (用户名: admin, 密码: admin123)');
  }
}
