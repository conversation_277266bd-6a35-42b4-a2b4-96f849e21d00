import { Provide } from '@midwayjs/core';
import { UploadFileInfo } from '@midwayjs/upload';
import { join } from 'path';
import { createWriteStream, existsSync, mkdirSync } from 'fs';
import { v4 as uuidv4 } from 'uuid';

@Provide()
export class UploadService {
  /**
   * 上传单个文件
   */
  async uploadFile(file: UploadFileInfo<any>): Promise<string> {
    // 生成唯一文件名
    const fileExtension = this.getFileExtension(file.filename);
    const fileName = `${uuidv4()}${fileExtension}`;
    
    // 按日期创建目录
    const today = new Date();
    const dateDir = `${today.getFullYear()}/${String(today.getMonth() + 1).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}`;
    const uploadDir = join(process.cwd(), 'public', 'uploads', dateDir);
    
    // 确保目录存在
    if (!existsSync(uploadDir)) {
      mkdirSync(uploadDir, { recursive: true });
    }
    
    // 文件保存路径
    const filePath = join(uploadDir, fileName);
    const relativePath = `/public/uploads/${dateDir}/${fileName}`;
    
    // 保存文件
    const writeStream = createWriteStream(filePath);

    return new Promise((resolve, reject) => {
      if (typeof file.data === 'string') {
        // 如果是字符串，直接写入
        writeStream.write(file.data);
        writeStream.end();
      } else {
        // 如果是流，使用pipe
        file.data.pipe(writeStream);
      }

      writeStream.on('finish', () => {
        resolve(relativePath);
      });

      writeStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 上传多个文件
   */
  async uploadFiles(files: UploadFileInfo<any>[]): Promise<string[]> {
    const uploadPromises = files.map(file => this.uploadFile(file));
    return await Promise.all(uploadPromises);
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  /**
   * 验证文件类型
   */
  validateFileType(filename: string, allowedTypes: string[] = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']): boolean {
    const extension = this.getFileExtension(filename).toLowerCase();
    return allowedTypes.includes(extension);
  }

  /**
   * 验证文件大小
   */
  validateFileSize(fileSize: number, maxSize: number = 50 * 1024 * 1024): boolean { // 默认50MB
    return fileSize <= maxSize;
  }
}
