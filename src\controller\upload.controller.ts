import { Controller, Post, Inject, Files } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { UploadFileInfo } from '@midwayjs/upload';
import { UploadService } from '../service/upload.service';

@Controller('/api/upload')
export class UploadController {
  @Inject()
  ctx: Context;

  @Inject()
  uploadService: UploadService;

  /**
   * 上传单个文件
   */
  @Post('/file')
  async uploadFile(@Files() files: UploadFileInfo<any>[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.uploadService.validateFileType(file.filename)) {
      throw new Error('不支持的文件类型，仅支持图片文件');
    }

    // 验证文件大小
    const fileSize =
      typeof file.data === 'string' ? Buffer.byteLength(file.data) : 0;
    if (!this.uploadService.validateFileSize(fileSize)) {
      throw new Error('文件大小超过限制（50MB）');
    }

    const url = await this.uploadService.uploadFile(file);

    return {
      url,
      filename: file.filename,
      size: fileSize,
    };
  }

  /**
   * 上传多个文件
   */
  @Post('/files')
  async uploadFiles(@Files() files: UploadFileInfo<any>[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要上传的文件');
    }

    // 验证所有文件
    for (const file of files) {
      if (!this.uploadService.validateFileType(file.filename)) {
        throw new Error(`文件 ${file.filename} 类型不支持，仅支持图片文件`);
      }

      const fileSize =
        typeof file.data === 'string' ? Buffer.byteLength(file.data) : 0;
      if (!this.uploadService.validateFileSize(fileSize)) {
        throw new Error(`文件 ${file.filename} 大小超过限制（50MB）`);
      }
    }

    const urls = await this.uploadService.uploadFiles(files);

    const result = files.map((file, index) => {
      const fileSize =
        typeof file.data === 'string' ? Buffer.byteLength(file.data) : 0;
      return {
        url: urls[index],
        filename: file.filename,
        size: fileSize,
      };
    });

    return result;
  }
}
