import { Controller, Post, Put, Del, Get, Body, Param, Query, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { WaterSystemService } from '../../service/water-system.service';
import { PageQueryDTO } from '../../dto/common.dto';
import { CreateWaterSystemDTO, UpdateWaterSystemDTO } from '../../dto/entity.dto';

/**
 * 水系管理控制器
 */
@Controller('/admin/water-system', { middleware: [JwtMiddleware, AuthMiddleware] })
export class AdminWaterSystemController {
  @Inject()
  waterSystemService: WaterSystemService;

  /**
   * 创建水系
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateWaterSystemDTO) {
    const data = await this.waterSystemService.createWaterSystem(createDto);
    return data;
  }

  /**
   * 更新水系
   */
  @Put('/:id')
  @Validate()
  async update(@Param('id') id: number, @Body() updateDto: UpdateWaterSystemDTO) {
    const data = await this.waterSystemService.updateWaterSystem(id, updateDto);
    return data;
  }

  /**
   * 删除水系
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    await this.waterSystemService.deleteWaterSystem(id);
    return { message: '删除成功' };
  }

  /**
   * 获取水系列表
   */
  @Get('/')
  @Validate()
  async getList(@Query() query: PageQueryDTO) {
    const data = await this.waterSystemService.findWaterSystemList(query);
    return data;
  }

  /**
   * 获取水系详情
   */
  @Get('/:id')
  async getDetail(@Param('id') id: number) {
    const data = await this.waterSystemService.findById(id);
    if (!data) {
      throw new Error('水系不存在');
    }
    return data;
  }

  /**
   * 批量导入水系
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { waterSystems: CreateWaterSystemDTO[] }) {
    await this.waterSystemService.batchImportWaterSystems(data.waterSystems);
    return { message: '批量导入成功' };
  }

  /**
   * 获取水系统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query('regionId') regionId?: number) {
    const data = await this.waterSystemService.getStatistics(regionId);
    return data;
  }

  /**
   * 根据区域获取水系
   */
  @Get('/by-region/:regionId')
  async getByRegion(@Param('regionId') regionId: number) {
    const data = await this.waterSystemService.findByRegion(regionId);
    return data;
  }

  /**
   * 根据长度/面积范围查询
   */
  @Get('/by-length-area')
  async getByLengthArea(
    @Query('minLength') minLength?: number,
    @Query('maxLength') maxLength?: number
  ) {
    const data = await this.waterSystemService.findByLengthArea(minLength, maxLength);
    return data;
  }
}
