import { Controller, Post, Get, Body, Inject } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { UserService } from '../../service/user.service';
import { LoginDTO } from '../../dto/user.dto';

/**
 * 管理后台认证控制器
 */
@Controller('/admin/auth')
export class AdminAuthController {
  @Inject()
  userService: UserService;

  /**
   * 管理员登录
   */
  @Post('/login')
  @Validate()
  async login(@Body() loginDto: LoginDTO) {
    const result = await this.userService.login(loginDto);
    return result;
  }

  /**
   * 获取当前用户信息
   */
  @Get('/profile', { middleware: [JwtMiddleware] })
  async getProfile() {
    // TODO: 从JWT中获取用户信息
    return {
      id: 1,
      username: 'admin',
      role: 'admin',
      permissions: ['*'],
    };
  }

  /**
   * 退出登录
   */
  @Post('/logout', { middleware: [JwtMiddleware] })
  async logout() {
    // TODO: 实现登出逻辑（如果需要）
    return { message: '退出成功' };
  }

  /**
   * 刷新Token
   */
  @Post('/refresh', { middleware: [JwtMiddleware] })
  async refreshToken() {
    // TODO: 实现Token刷新逻辑
    return { message: 'Token刷新成功' };
  }
}
