# 资源管理 API 文档

## 概述

资源管理模块提供文件上传功能，支持图片文件的上传和管理。

---

## 统一响应格式

### 成功响应
所有接口成功时返回统一格式：
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
所有接口失败时返回统一格式：
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 上传单个文件

### 接口信息

- **URL**: `/api/upload/file`
- **方法**: `POST`
- **认证**: 需要认证
- **Content-Type**: `multipart/form-data`

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | file | 是 | 要上传的文件 |

### 文件限制

- **支持格式**: .jpg, .jpeg, .png, .gif, .bmp, .webp
- **文件大小**: 最大50MB
- **数量限制**: 单次上传1个文件

### 请求示例

```bash
curl -X POST "http://localhost:7001/api/upload/file" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@/path/to/image.jpg"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "url": "/public/uploads/2024/01/15/image_1642234567890.jpg",
    "filename": "image.jpg",
    "size": 1024000
  }
}
```

---

## 上传多个文件

### 接口信息

- **URL**: `/api/upload/files`
- **方法**: `POST`
- **认证**: 需要认证
- **Content-Type**: `multipart/form-data`

### 请求头

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer {token} |

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| files | file[] | 是 | 要上传的文件列表 |

### 文件限制

- **支持格式**: .jpg, .jpeg, .png, .gif, .bmp, .webp
- **文件大小**: 每个文件最大50MB
- **数量限制**: 支持多文件同时上传

### 请求示例

```bash
curl -X POST "http://localhost:7001/api/upload/files" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "files=@/path/to/image1.jpg" \
  -F "files=@/path/to/image2.png"
```

### 响应示例

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "url": "/public/uploads/2024/01/15/image1_1642234567890.jpg",
      "filename": "image1.jpg",
      "size": 1024000
    },
    {
      "url": "/public/uploads/2024/01/15/image2_1642234567891.png",
      "filename": "image2.png",
      "size": 2048000
    }
  ]
}
```

---

## 错误处理

### 常见错误

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 请选择要上传的文件 | 未提供文件 |
| 400 | 不支持的文件类型，仅支持图片文件 | 文件格式不支持 |
| 400 | 文件大小超过限制（50MB） | 文件过大 |
| 400 | 文件 xxx 类型不支持，仅支持图片文件 | 多文件上传时某个文件格式不支持 |
| 400 | 文件 xxx 大小超过限制（50MB） | 多文件上传时某个文件过大 |
| 401 | 未提供认证令牌 | 缺少Authorization头 |
| 401 | 认证令牌无效或已过期 | Token无效 |
| 500 | 文件上传失败 | 服务器内部错误 |

### 错误响应示例

```json
{
  "errCode": 400,
  "msg": "不支持的文件类型，仅支持图片文件"
}
```

---

## 文件访问

### 静态文件访问

上传成功后，文件可通过返回的URL直接访问：

```
http://localhost:7001/public/uploads/2024/01/15/image_1642234567890.jpg
```

### URL结构说明

- **基础路径**: `/public/uploads/`
- **日期目录**: `YYYY/MM/DD/`
- **文件名**: `原文件名_时间戳.扩展名`

---

## 使用说明

### 前端集成示例

#### JavaScript/Fetch

```javascript
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/upload/file', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  const result = await response.json();
  return result;
};
```

#### jQuery

```javascript
$('#fileInput').change(function() {
  const file = this.files[0];
  const formData = new FormData();
  formData.append('file', file);
  
  $.ajax({
    url: '/api/upload/file',
    type: 'POST',
    data: formData,
    headers: {
      'Authorization': 'Bearer ' + token
    },
    processData: false,
    contentType: false,
    success: function(result) {
      console.log('上传成功:', result.data.url);
    },
    error: function(xhr) {
      console.error('上传失败:', xhr.responseJSON.msg);
    }
  });
});
```

### 注意事项

1. **认证要求**: 所有上传接口都需要有效的JWT Token
2. **文件格式**: 仅支持图片格式文件
3. **文件大小**: 单个文件最大50MB
4. **存储路径**: 文件按日期目录存储，便于管理
5. **文件命名**: 自动添加时间戳避免文件名冲突
6. **错误处理**: 上传前会验证文件类型和大小
7. **批量上传**: 支持多文件同时上传，失败时会返回具体错误信息

### 最佳实践

1. **文件验证**: 前端也应进行文件类型和大小验证
2. **进度显示**: 大文件上传时建议显示上传进度
3. **错误处理**: 妥善处理各种上传错误情况
4. **文件预览**: 上传前可提供文件预览功能
5. **重试机制**: 网络不稳定时可实现重试机制
